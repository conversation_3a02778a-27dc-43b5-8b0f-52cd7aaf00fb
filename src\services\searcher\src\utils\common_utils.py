import asyncio
from datetime import datetime, timedelta
from functools import wraps


# Helper function to run async functions with Click
def async_command(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))

    return wrapper


# Helper function to calculate Monday and Sunday dates
def get_week_start_end(
    date: datetime | None = None, end_date_delta=7
) -> tuple[datetime, datetime]:
    """Calculate week start and end dates."""
    if not date:
        date = datetime.now()
    weekday = date.weekday()
    days_to_monday = (0 - weekday) % 7
    start = date
    if days_to_monday != 0:
        start = date - timedelta(days=7 - days_to_monday)
    end = start + timedelta(days=end_date_delta)
    return start, end


# Helper function to calculate Monday and Sunday date strings
def get_week_start_end_str(
    date: datetime | None = None, end_date_delta=7
) -> tuple[str, str]:
    """Calculate week start and end date strings."""
    start, end = get_week_start_end(date, end_date_delta)
    start_str = start.strftime("%Y-%m-%d")
    end_str = end.strftime("%Y-%m-%d")
    return start_str, end_str


# Helper function to calculate period weeks list
def get_period_weeks(
    dateFrom: datetime, dateUpto: datetime | None = None
) -> list[tuple[datetime, datetime]]:
    """Calculate period weeks list."""
    weeks = []
    date = dateFrom
    start, end = get_week_start_end(date)
    weeks.append((start, end))
    while end < dateUpto:
        date = end + timedelta(days=1)
        start, end = get_week_start_end(date)
        weeks.append((start, end))
    return weeks
