#!/usr/bin/env python3
"""
Test script to verify the Windows event loop policy fix.

This script sends a POST request to the search endpoint to test if the
Windows subprocess issue has been resolved.
"""

import requests
import json
import time

def test_search_endpoint():
    """Test the search endpoint with a POST request."""
    
    # Server URL
    url = "http://localhost:8000/api/v1/search"
    
    # Test payload
    payload = {
        "area": "Psiri",
        "guests": 4,
        "check_in": "2025-06-01",
        "check_out": "2025-06-30"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    print("Testing search endpoint...")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print()
    
    try:
        # Send POST request
        response = requests.post(url, json=payload, headers=headers)
        
        print(f"Response Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Body: {response.text}")
        
        if response.status_code == 200:
            print("\n✅ Request successful! The Windows event loop fix appears to be working.")
            print("Check the server console for any error messages.")
        else:
            print(f"\n❌ Request failed with status code {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the server. Make sure it's running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error occurred: {e}")

if __name__ == "__main__":
    test_search_endpoint()
